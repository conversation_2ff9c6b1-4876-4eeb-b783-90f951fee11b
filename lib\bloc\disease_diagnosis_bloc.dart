import 'dart:typed_data';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:sam03/models/app_models.dart';
import 'package:sam03/repositories/disease_diagnosis_repository.dart';
import 'package:sam03/core/constants/app_constants.dart';

// ===== الأحداث (Events) =====

/// الحدث الأساسي لتشخيص الأمراض
abstract class DiseaseDiagnosisEvent extends Equatable {
  const DiseaseDiagnosisEvent();

  @override
  List<Object?> get props => [];
}

/// رفع صورة للتشخيص
class DiseaseDiagnosisEventUploadImage extends DiseaseDiagnosisEvent {
  final String userId;
  final Uint8List imageBytes;
  final String? plantPart;

  const DiseaseDiagnosisEventUploadImage({
    required this.userId,
    required this.imageBytes,
    this.plantPart,
  });

  @override
  List<Object?> get props => [userId, imageBytes, plantPart];
}

/// تحميل تاريخ التشخيص
class DiseaseDiagnosisEventLoadHistory extends DiseaseDiagnosisEvent {
  final String userId;

  const DiseaseDiagnosisEventLoadHistory(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// جلب معلومات المرض
class DiseaseDiagnosisEventGetDiseaseInfo extends DiseaseDiagnosisEvent {
  final String diseaseId;

  const DiseaseDiagnosisEventGetDiseaseInfo(this.diseaseId);

  @override
  List<Object?> get props => [diseaseId];
}

/// محاكاة معالجة التشخيص
class DiseaseDiagnosisEventProcessDiagnosis extends DiseaseDiagnosisEvent {
  final String resultId;

  const DiseaseDiagnosisEventProcessDiagnosis(this.resultId);

  @override
  List<Object?> get props => [resultId];
}

// ===== الحالات (States) =====

/// الحالة الأساسية لتشخيص الأمراض
abstract class DiseaseDiagnosisState extends Equatable {
  const DiseaseDiagnosisState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class DiseaseDiagnosisStateInitial extends DiseaseDiagnosisState {}

/// حالة التحميل
class DiseaseDiagnosisStateLoading extends DiseaseDiagnosisState {}

/// حالة رفع الصورة
class DiseaseDiagnosisStateImageUploading extends DiseaseDiagnosisState {}

/// حالة رفع الصورة بنجاح
class DiseaseDiagnosisStateImageUploaded extends DiseaseDiagnosisState {
  final DiagnosisResult result;

  const DiseaseDiagnosisStateImageUploaded(this.result);

  @override
  List<Object?> get props => [result];
}

/// حالة معالجة التشخيص
class DiseaseDiagnosisStateProcessing extends DiseaseDiagnosisState {
  final DiagnosisResult result;

  const DiseaseDiagnosisStateProcessing(this.result);

  @override
  List<Object?> get props => [result];
}

/// حالة اكتمال التشخيص
class DiseaseDiagnosisStateCompleted extends DiseaseDiagnosisState {
  final DiagnosisResult result;
  final DiseaseInfo? diseaseInfo;

  const DiseaseDiagnosisStateCompleted({
    required this.result,
    this.diseaseInfo,
  });

  @override
  List<Object?> get props => [result, diseaseInfo];
}

/// حالة تحميل التاريخ
class DiseaseDiagnosisStateHistoryLoaded extends DiseaseDiagnosisState {
  final List<DiagnosisResult> history;

  const DiseaseDiagnosisStateHistoryLoaded(this.history);

  @override
  List<Object?> get props => [history];
}

/// حالة تحميل معلومات المرض
class DiseaseDiagnosisStateDiseaseInfoLoaded extends DiseaseDiagnosisState {
  final DiseaseInfo diseaseInfo;

  const DiseaseDiagnosisStateDiseaseInfoLoaded(this.diseaseInfo);

  @override
  List<Object?> get props => [diseaseInfo];
}

/// حالة الخطأ
class DiseaseDiagnosisStateError extends DiseaseDiagnosisState {
  final String message;

  const DiseaseDiagnosisStateError(this.message);

  @override
  List<Object?> get props => [message];
}

// ===== BLoC =====

/// BLoC لإدارة تشخيص أمراض النباتات
class DiseaseDiagnosisBloc extends Bloc<DiseaseDiagnosisEvent, DiseaseDiagnosisState> {
  final DiseaseDiagnosisRepository repository;

  // متغيرات لتتبع الحالة الحالية
  final List<DiagnosisResult> _history = [];
  final Map<String, DiseaseInfo> _diseaseInfoCache = {};

  DiseaseDiagnosisBloc({
    required this.repository,
  }) : super(DiseaseDiagnosisStateInitial()) {
    on<DiseaseDiagnosisEventUploadImage>(_onUploadImage);
    on<DiseaseDiagnosisEventLoadHistory>(_onLoadHistory);
    on<DiseaseDiagnosisEventGetDiseaseInfo>(_onGetDiseaseInfo);
    on<DiseaseDiagnosisEventProcessDiagnosis>(_onProcessDiagnosis);
  }

  /// رفع صورة للتشخيص
  Future<void> _onUploadImage(
    DiseaseDiagnosisEventUploadImage event,
    Emitter<DiseaseDiagnosisState> emit,
  ) async {
    try {
      emit(DiseaseDiagnosisStateImageUploading());

      // رفع الصورة
      final imageUrl = await repository.uploadDiagnosisImage(
        event.imageBytes,
        event.userId,
      );

      // إنشاء طلب تشخيص
      final result = await repository.createDiagnosisRequest(
        userId: event.userId,
        imageUrl: imageUrl,
        plantPart: event.plantPart,
      );

      emit(DiseaseDiagnosisStateImageUploaded(result));

      // بدء معالجة التشخيص تلقائياً
      add(DiseaseDiagnosisEventProcessDiagnosis(result.id));
    } catch (e) {
      emit(DiseaseDiagnosisStateError(
        '${AppConstants.errorUploadingImage}: ${e.toString()}',
      ));
    }
  }

  /// تحميل تاريخ التشخيص
  Future<void> _onLoadHistory(
    DiseaseDiagnosisEventLoadHistory event,
    Emitter<DiseaseDiagnosisState> emit,
  ) async {
    try {
      emit(DiseaseDiagnosisStateLoading());

      _history.clear();
      _history.addAll(await repository.getDiagnosisHistory(event.userId));

      emit(DiseaseDiagnosisStateHistoryLoaded(_history));
    } catch (e) {
      emit(DiseaseDiagnosisStateError(
        '${AppConstants.errorLoadingDiagnosisHistory}: ${e.toString()}',
      ));
    }
  }

  /// جلب معلومات المرض
  Future<void> _onGetDiseaseInfo(
    DiseaseDiagnosisEventGetDiseaseInfo event,
    Emitter<DiseaseDiagnosisState> emit,
  ) async {
    try {
      // التحقق من الكاش أولاً
      if (_diseaseInfoCache.containsKey(event.diseaseId)) {
        emit(DiseaseDiagnosisStateDiseaseInfoLoaded(
          _diseaseInfoCache[event.diseaseId]!,
        ));
        return;
      }

      emit(DiseaseDiagnosisStateLoading());

      final diseaseInfo = await repository.getDiseaseInfo(event.diseaseId);
      if (diseaseInfo != null) {
        _diseaseInfoCache[event.diseaseId] = diseaseInfo;
        emit(DiseaseDiagnosisStateDiseaseInfoLoaded(diseaseInfo));
      } else {
        emit(const DiseaseDiagnosisStateError('لم يتم العثور على معلومات المرض'));
      }
    } catch (e) {
      emit(DiseaseDiagnosisStateError(
        '${AppConstants.errorLoadingDiseaseInfo}: ${e.toString()}',
      ));
    }
  }

  /// معالجة التشخيص (محاكاة)
  Future<void> _onProcessDiagnosis(
    DiseaseDiagnosisEventProcessDiagnosis event,
    Emitter<DiseaseDiagnosisState> emit,
  ) async {
    try {
      // محاكاة معالجة التشخيص
      emit(DiseaseDiagnosisStateProcessing(
        DiagnosisResult(
          id: event.resultId,
          userId: '',
          requestImageUrl: '',
          createdAt: DateTime.now(),
          status: 'processing',
        ),
      ));

      // محاكاة تأخير المعالجة
      await Future.delayed(const Duration(seconds: 3));

      // محاكاة نتيجة التشخيص
      final mockDiseaseId = 'disease_001';
      final confidence = 0.85;

      await repository.updateDiagnosisResult(
        resultId: event.resultId,
        status: 'completed',
        detectedDiseaseId: mockDiseaseId,
        confidence: confidence,
      );

      // جلب معلومات المرض
      final diseaseInfo = await repository.getDiseaseInfo(mockDiseaseId);

      final completedResult = DiagnosisResult(
        id: event.resultId,
        userId: '',
        requestImageUrl: '',
        createdAt: DateTime.now(),
        status: 'completed',
        detectedDiseaseId: mockDiseaseId,
        confidence: confidence,
      );

      emit(DiseaseDiagnosisStateCompleted(
        result: completedResult,
        diseaseInfo: diseaseInfo,
      ));
    } catch (e) {
      emit(DiseaseDiagnosisStateError(
        '${AppConstants.errorProcessingDiagnosis}: ${e.toString()}',
      ));
    }
  }

  // ===== دوال مساعدة =====

  /// الحصول على تاريخ التشخيص
  List<DiagnosisResult> get history => List.unmodifiable(_history);

  /// الحصول على معلومات المرض من الكاش
  DiseaseInfo? getCachedDiseaseInfo(String diseaseId) {
    return _diseaseInfoCache[diseaseId];
  }
}
