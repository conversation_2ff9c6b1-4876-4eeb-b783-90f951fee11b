import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:image_picker/image_picker.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/market_bloc.dart';
import '../models/app_models.dart';

class EditProductScreen extends StatefulWidget {
  final Product product;

  const EditProductScreen({super.key, required this.product});

  @override
  State<EditProductScreen> createState() => _EditProductScreenState();
}

class _EditProductScreenState extends State<EditProductScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  final List<String> _imageUrls = [];
  final List<TempFileData> _newImages = [];

  @override
  void initState() {
    super.initState();
    _imageUrls.addAll(widget.product.imageUrls);
  }

  @override
  Widget build(BuildContext context) {
    final authState = context.select((AuthBloc bloc) => bloc.state);
    final currentUser =
        authState is AuthStateAuthenticated ? authState.user : null;

    if (currentUser == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('تعديل المنتج'),
        ),
        body: const Center(
          child: Text('يجب تسجيل الدخول لتعديل المنتج'),
        ),
      );
    }

    // التحقق من أن المستخدم هو صاحب المنتج
    if (currentUser.id != widget.product.sellerId) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('تعديل المنتج'),
        ),
        body: const Center(
          child: Text('لا يمكنك تعديل هذا المنتج لأنك لست المالك'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل المنتج'),
      ),
      body: BlocListener<MarketBloc, MarketState>(
        listener: (context, state) {
          if (state is MarketStateError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          } else if (state is MarketStateProductUpdated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم تحديث المنتج بنجاح')),
            );
            Navigator.pop(context);
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: FormBuilder(
            key: _formKey,
            initialValue: {
              'name': widget.product.name,
              'description': widget.product.description,
              'price': widget.product.price.toString(),
              'unit': widget.product.unit,
              'quantity': widget.product.quantity.toString(),
              'category': widget.product.category,
              'location': widget.product.location,
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // منطقة عرض الصور
                const Text(
                  'صور المنتج',
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 8),
                GestureDetector(
                  onTap: _pickImages,
                  child: Container(
                    height: 120,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Colors.grey.withOpacity(0.5),
                        width: 1.0,
                        style: BorderStyle.solid,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: _imageUrls.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.camera_alt,
                                  size: 40,
                                  color: Colors.grey.withOpacity(0.5),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'انقر لاختيار صور',
                                  style: TextStyle(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withOpacity(0.6),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            scrollDirection: Axis.horizontal,
                            padding: const EdgeInsets.all(8),
                            itemCount: _imageUrls.length + 1, // +1 لزر الإضافة
                            itemBuilder: (context, index) {
                              if (index == _imageUrls.length) {
                                return GestureDetector(
                                  onTap: _pickImages,
                                  child: Container(
                                    width: 80,
                                    margin: const EdgeInsets.only(right: 8),
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        color: Colors.grey.withOpacity(0.5),
                                        width: 1.0,
                                        style: BorderStyle.solid,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      Icons.add,
                                      size: 40,
                                      color: Colors.grey.withOpacity(0.5),
                                    ),
                                  ),
                                );
                              }

                              return Stack(
                                children: [
                                  Container(
                                    width: 80,
                                    height: 80,
                                    margin: const EdgeInsets.only(right: 8),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      image: DecorationImage(
                                        image: NetworkImage(_imageUrls[index]),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                    top: 0,
                                    right: 0,
                                    child: GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          _imageUrls.removeAt(index);
                                        });
                                      },
                                      child: Container(
                                        width: 24,
                                        height: 24,
                                        decoration: const BoxDecoration(
                                          color: Colors.red,
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.close,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                  ),
                ),
                const SizedBox(height: 16),

                // حقول المنتج
                FormBuilderTextField(
                  name: 'name',
                  decoration: const InputDecoration(
                    labelText: 'اسم المنتج',
                    hintText: 'مثال: طماطم طازجة',
                  ),
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(
                        errorText: 'الرجاء إدخال اسم المنتج'),
                  ]),
                ),
                const SizedBox(height: 16),

                FormBuilderTextField(
                  name: 'description',
                  decoration: const InputDecoration(
                    labelText: 'وصف المنتج',
                    hintText: 'طماطم طازجة من المزرعة...',
                  ),
                  maxLines: 3,
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(
                        errorText: 'الرجاء إدخال وصف المنتج'),
                  ]),
                ),
                const SizedBox(height: 16),

                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: FormBuilderTextField(
                        name: 'price',
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: 'السعر',
                          hintText: '1000',
                        ),
                        validator: FormBuilderValidators.compose([
                          FormBuilderValidators.required(
                              errorText: 'الرجاء إدخال السعر'),
                          FormBuilderValidators.numeric(
                              errorText: 'الرجاء إدخال رقم صحيح'),
                        ]),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: FormBuilderDropdown<String>(
                        name: 'unit',
                        decoration: const InputDecoration(
                          labelText: 'الوحدة',
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: 'كجم',
                            child: Text('كجم'),
                          ),
                          DropdownMenuItem(
                            value: 'طن',
                            child: Text('طن'),
                          ),
                          DropdownMenuItem(
                            value: 'قطعة',
                            child: Text('قطعة'),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                FormBuilderTextField(
                  name: 'quantity',
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'الكمية المتوفرة',
                    hintText: '50',
                  ),
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(
                        errorText: 'الرجاء إدخال الكمية'),
                    FormBuilderValidators.numeric(
                        errorText: 'الرجاء إدخال رقم صحيح'),
                  ]),
                ),
                const SizedBox(height: 16),

                FormBuilderTextField(
                  name: 'location',
                  decoration: const InputDecoration(
                    labelText: 'الموقع',
                    hintText: 'صنعاء - شارع تعز',
                  ),
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(
                        errorText: 'الرجاء إدخال الموقع'),
                  ]),
                ),
                const SizedBox(height: 16),

                FormBuilderDropdown<String>(
                  name: 'category',
                  decoration: const InputDecoration(
                    labelText: 'الفئة',
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'طماطم',
                      child: Text('طماطم'),
                    ),
                    DropdownMenuItem(
                      value: 'قمح',
                      child: Text('قمح'),
                    ),
                    DropdownMenuItem(
                      value: 'بطاطا',
                      child: Text('بطاطا'),
                    ),
                    DropdownMenuItem(
                      value: 'بصل',
                      child: Text('بصل'),
                    ),
                    DropdownMenuItem(
                      value: 'تفاح',
                      child: Text('تفاح'),
                    ),
                    DropdownMenuItem(
                      value: 'بذور',
                      child: Text('بذور'),
                    ),
                    DropdownMenuItem(
                      value: 'أسمدة',
                      child: Text('أسمدة'),
                    ),
                    DropdownMenuItem(
                      value: 'معدات',
                      child: Text('معدات'),
                    ),
                  ],
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(
                        errorText: 'الرجاء اختيار الفئة'),
                  ]),
                ),
                const SizedBox(height: 24),

                // زر الحفظ
                SizedBox(
                  width: double.infinity,
                  child: BlocBuilder<MarketBloc, MarketState>(
                    builder: (context, state) {
                      return ElevatedButton(
                        onPressed:
                            state is MarketStateLoading ? null : _submitForm,
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: state is MarketStateLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Text(
                                  'حفظ التغييرات',
                                  style: TextStyle(fontSize: 16),
                                ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _pickImages() async {
    final ImagePicker picker = ImagePicker();

    try {
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        // قراءة بيانات الصورة
        final bytes = await image.readAsBytes();

        // إضافة الصورة إلى قائمة الصور الجديدة
        setState(() {
          _newImages.add(
            TempFileData(
              bytes: bytes,
              fileName: 'image${DateTime.now().millisecondsSinceEpoch}.jpg',
              mimeType: 'image/jpeg',
            ),
          );

          // إضافة الصورة المحلية إلى القائمة (سيتم رفعها لاحقاً)
          // نستخدم مؤقتاً رابط وهمي سيتم استبداله عند الحفظ
          _imageUrls
              .add('temp_image_${DateTime.now().millisecondsSinceEpoch}.jpg');
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    }
  }

  void _submitForm() {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final formData = _formKey.currentState!.value;

      if (_imageUrls.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('الرجاء إضافة صورة واحدة على الأقل')),
        );
        return;
      }

      // تحديث المنتج
      context.read<MarketBloc>().add(
            MarketEventUpdateProduct(
              id: widget.product.id,
              name: formData['name'],
              description: formData['description'],
              price: double.parse(formData['price']),
              unit: formData['unit'],
              quantity: double.parse(formData['quantity']),
              category: formData['category'],
              imageUrls: _imageUrls,
              location: formData['location'],
              sellerPhone: widget.product.sellerPhone,
            ),
          );
    }
  }
}
