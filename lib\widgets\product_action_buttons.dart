import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../models/app_models.dart';
import '../bloc/auth_bloc.dart';
import '../screens/edit_product_screen.dart';

/// أزرار التحكم بالمنتج (شراء، تعديل، حذف)
class ProductActionButtons extends StatelessWidget {
  final Product product;
  final VoidCallback onDelete;

  const ProductActionButtons({
    super.key,
    required this.product,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    // استخدام متغير لتخزين حالة المستخدم
    final authState = context.read<AuthBloc>().state;
    final currentUser =
        authState is AuthStateAuthenticated ? authState.user : null;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: currentUser != null && currentUser.id == product.sellerId
          ? Row(
              // إذا كان المستخدم هو صاحب المنتج، نعرض أزرار التعديل والحذف
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.edit),
                    label: const Text('تعديل'),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              EditProductScreen(product: product),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                    ),
                    icon: const Icon(Icons.delete),
                    label: const Text('حذف'),
                    onPressed: onDelete,
                  ),
                ),
              ],
            )
          : Row(
              // إذا كان المستخدم زائر أو مستخدم آخر، نعرض زر الشراء
              children: [
                if (currentUser != null) ...[
                  IconButton(
                    icon: const Icon(Icons.chat),
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content: Text('سيتم إضافة ميزة المحادثات قريباً')),
                      );
                    },
                  ),
                  const SizedBox(width: 8),
                ],
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.shopping_cart),
                    label: const Text('شراء'),
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content: Text('تم إضافة المنتج إلى سلة المشتريات')),
                      );
                    },
                  ),
                ),
              ],
            ),
    );
  }
}
