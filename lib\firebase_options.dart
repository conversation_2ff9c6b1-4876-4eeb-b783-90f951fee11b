// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;


class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for ios - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyC7vSXr5dhZ1Rq7ff8k7j7SmXyJAg3Ap-0',
    appId: '1:607476429461:web:61134659310e3fedef619c',
    messagingSenderId: '607476429461',
    projectId: 'sam03-b6433',
    authDomain: 'sam03-b6433.firebaseapp.com',
    storageBucket: 'sam03-b6433.firebasestorage.app',
    measurementId: 'G-FDXKTN6YQR',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAvMMSr6SEBVLyzdXoBtpFFn2G-MxaSUJ0',
    appId: '1:607476429461:android:b5d54a050d68f9fcef619c',
    messagingSenderId: '607476429461',
    projectId: 'sam03-b6433',
    storageBucket: 'sam03-b6433.firebasestorage.app',
  );
}
