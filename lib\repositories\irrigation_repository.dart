import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:sam03/models/app_models.dart';

/// مستودع بيانات نظام الري الذكي
/// يتولى جميع عمليات الوصول للبيانات المتعلقة بأنظمة الري
class IrrigationRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // ===== أنظمة الري =====

  /// جلب جميع أنظمة الري للمستخدم
  Future<List<IrrigationSystem>> getIrrigationSystems(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('irrigation_systems')
          .where('userId', isEqualTo: userId)
          .orderBy('lastUpdate', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => IrrigationSystem.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب أنظمة الري: $e');
    }
  }

  /// جلب نظام ري محدد
  Future<IrrigationSystem?> getIrrigationSystem(String systemId) async {
    try {
      final doc = await _firestore
          .collection('irrigation_systems')
          .doc(systemId)
          .get();

      if (!doc.exists) return null;
      return IrrigationSystem.fromFirestore(doc);
    } catch (e) {
      throw Exception('فشل في جلب نظام الري: $e');
    }
  }

  /// إضافة نظام ري جديد
  Future<IrrigationSystem> addIrrigationSystem({
    required String userId,
    required String name,
    required String type,
    required String serialNumber,
    String? description,
    String? location,
    String? cropType,
  }) async {
    try {
      final now = DateTime.now();
      final defaultSensors = SensorData(
        soilMoisture: 0.0,
        temperature: 0.0,
        humidity: 0.0,
        waterLevel: 0.0,
        batteryLevel: 1.0,
        rainStatus: false,
      );

      final systemData = {
        'userId': userId,
        'name': name,
        'type': type,
        'status': 'inactive',
        'serialNumber': serialNumber,
        'lastUpdate': Timestamp.fromDate(now),
        'description': description,
        'location': location,
        'cropType': cropType,
        'sensors': defaultSensors.toMap(),
        'createdAt': Timestamp.fromDate(now),
      };

      final docRef = await _firestore
          .collection('irrigation_systems')
          .add(systemData);

      final doc = await docRef.get();
      return IrrigationSystem.fromFirestore(doc);
    } catch (e) {
      throw Exception('فشل في إضافة نظام الري: $e');
    }
  }

  /// تحديث بيانات نظام الري
  Future<void> updateIrrigationSystem(IrrigationSystem system) async {
    try {
      await _firestore
          .collection('irrigation_systems')
          .doc(system.id)
          .update(system.toFirestore());
    } catch (e) {
      throw Exception('فشل في تحديث نظام الري: $e');
    }
  }

  /// تحديث بيانات الحساسات
  Future<void> updateSensorData(String systemId, SensorData sensorData) async {
    try {
      await _firestore
          .collection('irrigation_systems')
          .doc(systemId)
          .update({
        'sensors': sensorData.toMap(),
        'lastUpdate': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('فشل في تحديث بيانات الحساسات: $e');
    }
  }

  /// حذف نظام الري
  Future<void> deleteIrrigationSystem(String systemId) async {
    try {
      await _firestore
          .collection('irrigation_systems')
          .doc(systemId)
          .delete();
    } catch (e) {
      throw Exception('فشل في حذف نظام الري: $e');
    }
  }

  // ===== سجلات الري =====

  /// جلب سجلات الري لنظام محدد
  Future<List<IrrigationRecord>> getIrrigationRecords(
    String systemId, {
    int limit = 50,
  }) async {
    try {
      final snapshot = await _firestore
          .collection('irrigation_records')
          .where('systemId', isEqualTo: systemId)
          .orderBy('startTime', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => IrrigationRecord.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب سجلات الري: $e');
    }
  }

  /// إضافة سجل ري جديد
  Future<IrrigationRecord> addIrrigationRecord({
    required String systemId,
    required DateTime startTime,
    DateTime? endTime,
    required int duration,
    required double waterUsed,
    required IrrigationTrigger trigger,
    String? notes,
    Map<String, dynamic>? sensorDataAtStart,
  }) async {
    try {
      final recordData = {
        'systemId': systemId,
        'startTime': Timestamp.fromDate(startTime),
        'endTime': endTime != null ? Timestamp.fromDate(endTime) : null,
        'duration': duration,
        'waterUsed': waterUsed,
        'trigger': trigger.toString().split('.').last,
        'notes': notes,
        'sensorDataAtStart': sensorDataAtStart,
        'createdAt': Timestamp.fromDate(DateTime.now()),
      };

      final docRef = await _firestore
          .collection('irrigation_records')
          .add(recordData);

      final doc = await docRef.get();
      return IrrigationRecord.fromFirestore(doc);
    } catch (e) {
      throw Exception('فشل في إضافة سجل الري: $e');
    }
  }

  /// تحديث سجل الري
  Future<void> updateIrrigationRecord(IrrigationRecord record) async {
    try {
      await _firestore
          .collection('irrigation_records')
          .doc(record.id)
          .update(record.toFirestore());
    } catch (e) {
      throw Exception('فشل في تحديث سجل الري: $e');
    }
  }

  // ===== إعدادات الري =====

  /// جلب إعدادات الري لنظام محدد
  Future<IrrigationSettings?> getIrrigationSettings(String systemId) async {
    try {
      final doc = await _firestore
          .collection('irrigation_settings')
          .doc(systemId)
          .get();

      if (!doc.exists) return null;
      return IrrigationSettings.fromFirestore(doc);
    } catch (e) {
      throw Exception('فشل في جلب إعدادات الري: $e');
    }
  }

  /// حفظ إعدادات الري
  Future<void> saveIrrigationSettings(IrrigationSettings settings) async {
    try {
      await _firestore
          .collection('irrigation_settings')
          .doc(settings.systemId)
          .set(settings.toFirestore());
    } catch (e) {
      throw Exception('فشل في حفظ إعدادات الري: $e');
    }
  }

  // ===== إحصائيات الري =====

  /// جلب إحصائيات استهلاك المياه لفترة محددة
  Future<Map<String, dynamic>> getWaterUsageStats(
    String systemId, {
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final snapshot = await _firestore
          .collection('irrigation_records')
          .where('systemId', isEqualTo: systemId)
          .where('startTime', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('startTime', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .get();

      final records = snapshot.docs
          .map((doc) => IrrigationRecord.fromFirestore(doc))
          .toList();

      double totalWaterUsed = 0;
      int totalDuration = 0;
      int totalSessions = records.length;

      for (final record in records) {
        totalWaterUsed += record.waterUsed;
        totalDuration += record.duration;
      }

      return {
        'totalWaterUsed': totalWaterUsed,
        'totalDuration': totalDuration,
        'totalSessions': totalSessions,
        'averageWaterPerSession': totalSessions > 0 ? totalWaterUsed / totalSessions : 0,
        'averageDurationPerSession': totalSessions > 0 ? totalDuration / totalSessions : 0,
      };
    } catch (e) {
      throw Exception('فشل في جلب إحصائيات الري: $e');
    }
  }

  /// جلب بيانات الرسم البياني لرطوبة التربة
  Future<List<Map<String, dynamic>>> getSoilMoistureChart(
    String systemId, {
    int days = 7,
  }) async {
    try {
      final endDate = DateTime.now();
      final startDate = endDate.subtract(Duration(days: days));

      final snapshot = await _firestore
          .collection('sensor_readings')
          .where('systemId', isEqualTo: systemId)
          .where('timestamp', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('timestamp', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('timestamp')
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'timestamp': (data['timestamp'] as Timestamp).toDate(),
          'soilMoisture': (data['soilMoisture'] ?? 0.0).toDouble(),
          'temperature': (data['temperature'] ?? 0.0).toDouble(),
          'humidity': (data['humidity'] ?? 0.0).toDouble(),
        };
      }).toList();
    } catch (e) {
      throw Exception('فشل في جلب بيانات الرسم البياني: $e');
    }
  }

  /// محاكاة تحديث بيانات الحساسات (للاختبار)
  Future<SensorData> simulateSensorUpdate(String systemId) async {
    try {
      // محاكاة قراءات عشوائية للحساسات
      final random = DateTime.now().millisecondsSinceEpoch % 100;
      final sensorData = SensorData(
        soilMoisture: 0.3 + (random % 40) / 100, // 30-70%
        temperature: 20.0 + (random % 20), // 20-40°C
        humidity: 0.4 + (random % 40) / 100, // 40-80%
        waterLevel: 0.5 + (random % 40) / 100, // 50-90%
        batteryLevel: 0.7 + (random % 30) / 100, // 70-100%
        rainStatus: random % 10 == 0, // 10% احتمال المطر
      );

      await updateSensorData(systemId, sensorData);
      return sensorData;
    } catch (e) {
      throw Exception('فشل في محاكاة تحديث الحساسات: $e');
    }
  }

  // ===== أنواع النباتات =====

  /// جلب جميع أنواع النباتات
  Future<List<PlantType>> getPlantTypes() async {
    try {
      final snapshot = await _firestore
          .collection('plant_types')
          .orderBy('arabicName')
          .get();

      return snapshot.docs
          .map((doc) => PlantType.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب أنواع النباتات: $e');
    }
  }

  /// جلب نوع نبات محدد
  Future<PlantType?> getPlantType(String plantTypeId) async {
    try {
      final doc = await _firestore
          .collection('plant_types')
          .doc(plantTypeId)
          .get();

      if (!doc.exists) return null;
      return PlantType.fromFirestore(doc);
    } catch (e) {
      throw Exception('فشل في جلب نوع النبات: $e');
    }
  }

  /// البحث في أنواع النباتات
  Future<List<PlantType>> searchPlantTypes(String query) async {
    try {
      final snapshot = await _firestore
          .collection('plant_types')
          .where('arabicName', isGreaterThanOrEqualTo: query)
          .where('arabicName', isLessThan: query + '\uf8ff')
          .get();

      return snapshot.docs
          .map((doc) => PlantType.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('فشل في البحث عن أنواع النباتات: $e');
    }
  }

  /// إضافة نوع نبات جديد
  Future<PlantType> addPlantType(PlantType plantType) async {
    try {
      final docRef = await _firestore
          .collection('plant_types')
          .add(plantType.toFirestore());

      final doc = await docRef.get();
      return PlantType.fromFirestore(doc);
    } catch (e) {
      throw Exception('فشل في إضافة نوع النبات: $e');
    }
  }

  // ===== إحصائيات استخدام المياه =====

  /// جلب إحصائيات استخدام المياه لنظام محدد
  Future<List<WaterUsageStats>> getWaterUsageHistory(
    String systemId, {
    DateTime? startDate,
    DateTime? endDate,
    int limit = 30,
  }) async {
    try {
      Query query = _firestore
          .collection('water_usage_stats')
          .where('systemId', isEqualTo: systemId);

      if (startDate != null) {
        query = query.where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('date', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      final snapshot = await query
          .orderBy('date', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => WaterUsageStats.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب إحصائيات استخدام المياه: $e');
    }
  }

  /// إضافة إحصائيات استخدام المياه
  Future<WaterUsageStats> addWaterUsageStats(WaterUsageStats stats) async {
    try {
      final docRef = await _firestore
          .collection('water_usage_stats')
          .add(stats.toFirestore());

      final doc = await docRef.get();
      return WaterUsageStats.fromFirestore(doc);
    } catch (e) {
      throw Exception('فشل في إضافة إحصائيات استخدام المياه: $e');
    }
  }

  /// حساب إحصائيات استخدام المياه اليومية
  Future<WaterUsageStats> calculateDailyWaterUsage(
    String systemId,
    DateTime date,
  ) async {
    try {
      // جلب جميع سجلات الري لليوم المحدد
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final recordsSnapshot = await _firestore
          .collection('irrigation_records')
          .where('systemId', isEqualTo: systemId)
          .where('startTime', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('startTime', isLessThan: Timestamp.fromDate(endOfDay))
          .get();

      final records = recordsSnapshot.docs
          .map((doc) => IrrigationRecord.fromFirestore(doc))
          .toList();

      // حساب الإحصائيات
      double totalWaterUsed = 0;
      double totalDuration = 0;
      Map<String, double> hourlyUsage = {};

      for (final record in records) {
        totalWaterUsed += record.waterUsed;
        totalDuration += record.duration;

        // تجميع الاستخدام بالساعة
        final hour = record.startTime.hour.toString();
        hourlyUsage[hour] = (hourlyUsage[hour] ?? 0) + record.waterUsed;
      }

      final averageSessionDuration = records.isNotEmpty ? totalDuration / records.length : 0;
      final efficiency = _calculateIrrigationEfficiency(records);
      final costEstimate = totalWaterUsed * 0.5; // تقدير: 0.5 ريال لكل لتر

      final stats = WaterUsageStats(
        id: '',
        systemId: systemId,
        date: date,
        totalWaterUsed: totalWaterUsed,
        totalIrrigationSessions: records.length,
        averageSessionDuration: averageSessionDuration.toDouble(),
        efficiency: efficiency,
        hourlyUsage: hourlyUsage,
        costEstimate: costEstimate,
        notes: 'إحصائيات محسوبة تلقائياً',
      );

      return await addWaterUsageStats(stats);
    } catch (e) {
      throw Exception('فشل في حساب إحصائيات استخدام المياه: $e');
    }
  }

  /// حساب كفاءة الري
  double _calculateIrrigationEfficiency(List<IrrigationRecord> records) {
    if (records.isEmpty) return 0.0;

    // خوارزمية بسيطة لحساب الكفاءة بناءً على:
    // - نسبة الري التلقائي مقابل اليدوي
    // - توقيت الري (الري في الصباح الباكر أكثر كفاءة)
    // - مدة الري (الري القصير المتكرر أكثر كفاءة)

    double efficiencyScore = 0.0;
    int autoCount = 0;
    int morningCount = 0;
    int optimalDurationCount = 0;

    for (final record in records) {
      // نقاط للري التلقائي
      if (record.trigger == IrrigationTrigger.auto ||
          record.trigger == IrrigationTrigger.smart) {
        autoCount++;
      }

      // نقاط للري في الصباح الباكر (5-8 صباحاً)
      final hour = record.startTime.hour;
      if (hour >= 5 && hour <= 8) {
        morningCount++;
      }

      // نقاط للمدة المثلى (15-45 دقيقة)
      if (record.duration >= 15 && record.duration <= 45) {
        optimalDurationCount++;
      }
    }

    final autoRatio = autoCount / records.length;
    final morningRatio = morningCount / records.length;
    final optimalDurationRatio = optimalDurationCount / records.length;

    efficiencyScore = (autoRatio * 0.4) + (morningRatio * 0.3) + (optimalDurationRatio * 0.3);

    return efficiencyScore.clamp(0.0, 1.0);
  }

  // ===== قراءات الحساسات التاريخية =====

  /// إضافة قراءة حساسات جديدة
  Future<SensorReading> addSensorReading(
    String systemId,
    SensorData sensorData, {
    String? notes,
  }) async {
    try {
      final isAlert = _checkForAlerts(sensorData);

      final reading = SensorReading(
        id: '',
        systemId: systemId,
        timestamp: DateTime.now(),
        sensorData: sensorData,
        notes: notes,
        isAlert: isAlert,
      );

      final docRef = await _firestore
          .collection('sensor_readings')
          .add(reading.toFirestore());

      final doc = await docRef.get();
      return SensorReading.fromFirestore(doc);
    } catch (e) {
      throw Exception('فشل في إضافة قراءة الحساسات: $e');
    }
  }

  /// جلب قراءات الحساسات التاريخية
  Future<List<SensorReading>> getSensorReadings(
    String systemId, {
    DateTime? startDate,
    DateTime? endDate,
    int limit = 100,
  }) async {
    try {
      Query query = _firestore
          .collection('sensor_readings')
          .where('systemId', isEqualTo: systemId);

      if (startDate != null) {
        query = query.where('timestamp', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('timestamp', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      final snapshot = await query
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => SensorReading.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب قراءات الحساسات: $e');
    }
  }

  /// فحص التنبيهات في قراءات الحساسات
  bool _checkForAlerts(SensorData sensorData) {
    // فحص القيم غير الطبيعية
    if (sensorData.soilMoisture < 0.2) return true; // رطوبة منخفضة جداً
    if (sensorData.soilMoisture > 0.9) return true; // رطوبة عالية جداً
    if (sensorData.temperature < 5 || sensorData.temperature > 45) return true; // درجة حرارة غير طبيعية
    if (sensorData.waterLevel < 0.1) return true; // منسوب مياه منخفض
    if (sensorData.batteryLevel < 0.2) return true; // بطارية منخفضة

    return false;
  }

  // ===== التحقق من صحة البيانات =====

  /// التحقق من صحة بيانات نظام الري
  String? validateIrrigationSystem({
    required String name,
    required String type,
    required String serialNumber,
    String? location,
    String? cropType,
  }) {
    if (name.trim().isEmpty) {
      return 'اسم النظام مطلوب';
    }
    if (name.trim().length < 3) {
      return 'اسم النظام يجب أن يكون 3 أحرف على الأقل';
    }
    if (!['drip', 'sprinkler', 'micro'].contains(type)) {
      return 'نوع النظام غير صحيح';
    }
    if (serialNumber.trim().isEmpty) {
      return 'الرقم التسلسلي مطلوب';
    }
    if (serialNumber.trim().length < 5) {
      return 'الرقم التسلسلي يجب أن يكون 5 أحرف على الأقل';
    }
    if (location != null && location.trim().length > 100) {
      return 'الموقع يجب أن يكون أقل من 100 حرف';
    }
    if (cropType != null && cropType.trim().length > 50) {
      return 'نوع المحصول يجب أن يكون أقل من 50 حرف';
    }

    return null; // البيانات صحيحة
  }

  /// التحقق من صحة إعدادات الري
  String? validateIrrigationSettings(IrrigationSettings settings) {
    if (settings.moistureThreshold < 0.0 || settings.moistureThreshold > 1.0) {
      return 'عتبة الرطوبة يجب أن تكون بين 0 و 1';
    }
    if (settings.defaultDuration < 1 || settings.defaultDuration > 180) {
      return 'مدة الري يجب أن تكون بين 1 و 180 دقيقة';
    }
    if (settings.maxWaterPerDay < 0 || settings.maxWaterPerDay > 10000) {
      return 'الحد الأقصى للمياه يجب أن يكون بين 0 و 10000 لتر';
    }

    return null; // الإعدادات صحيحة
  }
}
