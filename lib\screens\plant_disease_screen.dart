import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'disease_diagnosis_screen.dart';

class PlantDiseaseScreen extends StatefulWidget {
  const PlantDiseaseScreen({super.key});

  @override
  State<PlantDiseaseScreen> createState() => _PlantDiseaseScreenState();
}

class _PlantDiseaseScreenState extends State<PlantDiseaseScreen> {
  String _selectedPlant = 'طماطم';
  File? _selectedImage;

  // قائمة النباتات المتاحة للتشخيص
  final List<Map<String, dynamic>> _plantsList = [
    {
      'name': 'طماطم',
      'icon': FontAwesomeIcons.apple,
      'color': Colors.red,
      'image': 'assets/images/tomato.jpg',
      'description': 'طماطم (بندورة)',
    },
    {
      'name': 'قمح',
      'icon': FontAwesomeIcons.wheatAwn,
      'color': Colors.amber,
      'image': 'assets/images/wheat.jpg',
      'description': 'قمح',
    },
    {
      'name': 'خيار',
      'icon': FontAwesomeIcons.seedling,
      'color': Colors.green,
      'image': 'assets/images/cucumber.jpg',
      'description': 'خيار',
    },
    {
      'name': 'فلفل',
      'icon': FontAwesomeIcons.pepperHot,
      'color': Colors.deepOrange,
      'image': 'assets/images/pepper.jpg',
      'description': 'فلفل',
    },
    {
      'name': 'بطاطس',
      'icon': FontAwesomeIcons.carrot,
      'color': Colors.brown,
      'image': 'assets/images/potato.jpg',
      'description': 'بطاطس',
    },
  ];

  // بيانات محاكاة لسجل التشخيص
  final List<Map<String, dynamic>> _diagnosisHistory = [
    {
      'date': DateTime.now().subtract(const Duration(days: 5)),
      'plant': 'طماطم',
      'disease': 'اللفحة المتأخرة',
      'confidence': 0.92,
      'image': 'https://picsum.photos/id/102/100',
      'status': 'تم العلاج',
    },
    {
      'date': DateTime.now().subtract(const Duration(days: 12)),
      'plant': 'خيار',
      'disease': 'البياض الدقيقي',
      'confidence': 0.88,
      'image': 'https://picsum.photos/id/197/100',
      'status': 'قيد المعالجة',
    },
    {
      'date': DateTime.now().subtract(const Duration(days: 20)),
      'plant': 'فلفل',
      'disease': 'سليم',
      'confidence': 0.95,
      'image': 'https://picsum.photos/id/292/100',
      'status': 'سليم',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // قائمة النباتات
            _buildPlantsSelector(),
            const SizedBox(height: 24),

            // بطاقة التشخيص
            _buildDiagnosisCard(),
            const SizedBox(height: 24),

            // سجل التشخيصات السابقة
            _buildRecentDiagnosisCard(),
          ],
        ),
      ),
    );
  }

  // بناء قائمة اختيار النباتات
  Widget _buildPlantsSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'اختر نوع النبات',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 110,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _plantsList.length,
            itemBuilder: (context, index) {
              final plant = _plantsList[index];
              final isSelected = plant['name'] == _selectedPlant;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedPlant = plant['name'];
                  });
                },
                child: Container(
                  width: 100,
                  margin: const EdgeInsets.only(right: 12),
                  decoration: BoxDecoration(
                    color: isSelected ? plant['color'].withOpacity(0.1) : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected ? plant['color'] : Colors.grey.shade300,
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        plant['icon'],
                        color: isSelected ? plant['color'] : Colors.grey,
                        size: 32,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        plant['name'],
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: isSelected ? plant['color'] : Colors.grey.shade700,
                        ),
                      ),
                      Text(
                        plant['description'],
                        style: TextStyle(
                          fontSize: 10,
                          color: isSelected ? plant['color'] : Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  // بناء بطاقة التشخيصات الأخيرة
  Widget _buildRecentDiagnosisCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'التشخيصات الأخيرة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    _showDiagnosisHistory();
                  },
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // قائمة التشخيصات الأخيرة
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _diagnosisHistory.length > 2 ? 2 : _diagnosisHistory.length,
              itemBuilder: (context, index) {
                final item = _diagnosisHistory[index];
                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  leading: CircleAvatar(
                    backgroundImage: AssetImage(item['image']),
                  ),
                  title: Text(
                    '${item['plant']} - ${item['disease']}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Text(
                    _getFormattedDate(item['date']),
                  ),
                  trailing: _buildStatusChip(item['status']),
                  onTap: () {
                    // عرض تفاصيل التشخيص السابق
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('عرض تفاصيل تشخيص ${item['disease']}')),
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  // بناء شريحة حالة التشخيص
  Widget _buildStatusChip(String status) {
    Color backgroundColor;
    Color textColor;

    switch (status) {
      case 'تم العلاج':
        backgroundColor = Colors.green.shade100;
        textColor = Colors.green.shade800;
        break;
      case 'قيد المعالجة':
        backgroundColor = Colors.orange.shade100;
        textColor = Colors.orange.shade800;
        break;
      case 'سليم':
        backgroundColor = Colors.blue.shade100;
        textColor = Colors.blue.shade800;
        break;
      default:
        backgroundColor = Colors.grey.shade100;
        textColor = Colors.grey.shade800;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildDiagnosisCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تشخيص مرض النبات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'التقط صورة للنبات المصاب لتشخيص المرض وتقديم الحلول المناسبة',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),

            // منطقة الصورة
            GestureDetector(
              onTap: () => _pickImage(source: ImageSource.gallery),
              child: Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: _selectedImage != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          _selectedImage!,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.add_a_photo,
                            size: 48,
                            color: Colors.grey.shade600,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'انقر لالتقاط صورة',
                            style: TextStyle(color: Colors.grey.shade600),
                          ),
                        ],
                      ),
              ),
            ),
            const SizedBox(height: 16),

            // أزرار التحكم
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('التقاط صورة'),
                    onPressed: () => _pickImage(source: ImageSource.camera),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.photo_library),
                    label: const Text('اختيار من المعرض'),
                    onPressed: () => _pickImage(source: ImageSource.gallery),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.search),
                label: const Text('تحليل الصورة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                onPressed: _selectedImage == null ? null : () => _analyzeImage(),
              ),
            ),
          ],
        ),
      ),
    );
  }



  void _pickImage({required ImageSource source}) async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: source);

    if (image != null) {
      setState(() {
        _selectedImage = File(image.path);
      });
    }
  }

  void _analyzeImage() {
    // انتقل إلى شاشة التشخيص مع الصورة المحددة
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DiseaseDiagnosisScreen(
          imageFile: _selectedImage!,
          plantType: _selectedPlant,
        ),
      ),
    ).then((result) {
      // تحديث سجل التشخيص إذا تم حفظ التشخيص
      if (result != null && result['saved'] == true) {
        setState(() {
          _diagnosisHistory.insert(0, {
            'date': DateTime.now(),
            'plant': result['plant'],
            'disease': result['disease'],
            'confidence': result['confidence'],
            'image': 'https://picsum.photos/id/292/100',
            'status': 'قيد المعالجة',
          });
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إضافة التشخيص إلى السجل')),
          );
        }
      }
    });
  }

  void _showDiagnosisHistory() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'سجل التشخيص',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.separated(
                  itemCount: _diagnosisHistory.length,
                  separatorBuilder: (context, index) => const Divider(),
                  itemBuilder: (context, index) {
                    final item = _diagnosisHistory[index];
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundImage: AssetImage(item['image']),
                      ),
                      title: Text(
                        '${item['plant']} - ${item['disease']}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: Text(
                        _getFormattedDate(item['date']),
                      ),
                      trailing: Chip(
                        label: Text('${(item['confidence'] * 100).toInt()}%'),
                        backgroundColor: _getConfidenceColor(item['confidence']),
                        labelStyle: const TextStyle(color: Colors.white, fontSize: 12),
                        padding: EdgeInsets.zero,
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        // يمكن إضافة منطق لعرض تفاصيل التشخيص السابق
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence < 0.7) return Colors.red;
    if (confidence < 0.85) return Colors.orange;
    return Colors.green;
  }

  String _getFormattedDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
