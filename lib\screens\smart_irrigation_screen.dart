import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:sam03/bloc/irrigation_bloc.dart';
import 'package:sam03/bloc/auth_bloc.dart';
import 'package:sam03/models/app_models.dart';
import 'package:sam03/repositories/irrigation_repository.dart';

/// شاشة الري الذكي المحدثة باستخدام BLoC
class SmartIrrigationScreen extends StatelessWidget {
  const SmartIrrigationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        final authState = context.read<AuthBloc>().state;
        String userId = '';

        if (authState is AuthStateAuthenticated) {
          userId = authState.user.id;
        }

        return IrrigationBloc(
          irrigationRepository: IrrigationRepository(),
        )..add(IrrigationEventLoadSystems(userId));
      },
      child: const _SmartIrrigationView(),
    );
  }
}

class _SmartIrrigationView extends StatefulWidget {
  const _SmartIrrigationView();

  @override
  State<_SmartIrrigationView> createState() => _SmartIrrigationViewState();
}

class _SmartIrrigationViewState extends State<_SmartIrrigationView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الري الذكي'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              final authState = context.read<AuthBloc>().state;
              if (authState is AuthStateAuthenticated) {
                context.read<IrrigationBloc>().add(
                      IrrigationEventLoadSystems(authState.user.id),
                    );
              }
            },
          ),
        ],
      ),
      body: BlocConsumer<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          if (state is IrrigationStateError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is IrrigationStateIrrigationStarted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم بدء الري بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
          } else if (state is IrrigationStateIrrigationStopped) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إيقاف الري'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is IrrigationStateLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state is IrrigationStateError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      final authState = context.read<AuthBloc>().state;
                      if (authState is AuthStateAuthenticated) {
                        context.read<IrrigationBloc>().add(
                              IrrigationEventLoadSystems(authState.user.id),
                            );
                      }
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (state is IrrigationStateSystemsLoaded) {
            if (state.systems.isEmpty) {
              return _buildEmptyState(context);
            }
            return _buildSystemsList(context, state.systems);
          }

          if (state is IrrigationStateSystemSelected ||
              state is IrrigationStateSensorsUpdated ||
              state is IrrigationStateIrrigationStarted ||
              state is IrrigationStateIrrigationStopped) {
            IrrigationSystem? system;
            bool isIrrigating = false;

            if (state is IrrigationStateSystemSelected) {
              system = state.system;
              isIrrigating = state.isIrrigating;
            } else if (state is IrrigationStateSensorsUpdated) {
              system = state.system;
            } else if (state is IrrigationStateIrrigationStarted) {
              system = context.read<IrrigationBloc>().selectedSystem;
              isIrrigating = true;
            } else if (state is IrrigationStateIrrigationStopped) {
              system = context.read<IrrigationBloc>().selectedSystem;
              isIrrigating = false;
            }

            if (system != null) {
              return _buildSystemDetails(context, system, isIrrigating);
            }
          }

          return _buildInitialState(context);
        },
      ),
    );
  }

  /// بناء الحالة الأولية
  Widget _buildInitialState(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FontAwesomeIcons.seedling,
            size: 64,
            color: Colors.green,
          ),
          SizedBox(height: 16),
          Text(
            'مرحباً بك في نظام الري الذكي',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'جاري تحميل أنظمة الري...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة عدم وجود أنظمة
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            FontAwesomeIcons.droplet,
            size: 64,
            color: Colors.blue,
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد أنظمة ري مسجلة',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'قم بإضافة نظام ري جديد للبدء',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('إضافة نظام ري جديد قيد التطوير'),
                ),
              );
            },
            icon: const Icon(Icons.add),
            label: const Text('إضافة نظام ري'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة الأنظمة
  Widget _buildSystemsList(
      BuildContext context, List<IrrigationSystem> systems) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'أنظمة الري المتاحة',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: systems.length,
            itemBuilder: (context, index) {
              final system = systems[index];
              return _buildSystemCard(context, system);
            },
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة النظام
  Widget _buildSystemCard(BuildContext context, IrrigationSystem system) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getSystemColor(system.type).withOpacity(0.1),
          child: Icon(
            _getSystemIcon(system.type),
            color: _getSystemColor(system.type),
          ),
        ),
        title: Text(
          system.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('النوع: ${_getSystemTypeName(system.type)}'),
            Text('الحالة: ${_getSystemStatusName(system.status)}'),
            Text(
                'رطوبة التربة: ${(system.sensors.soilMoisture * 100).toInt()}%'),
          ],
        ),
        trailing: Icon(
          system.status == 'active'
              ? Icons.check_circle
              : Icons.circle_outlined,
          color: system.status == 'active' ? Colors.green : Colors.grey,
        ),
        onTap: () {
          context.read<IrrigationBloc>().add(
                IrrigationEventSelectSystem(system.id),
              );
        },
      ),
    );
  }

  /// بناء تفاصيل النظام (مبسط)
  Widget _buildSystemDetails(
      BuildContext context, IrrigationSystem system, bool isIrrigating) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان النظام
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    system.name,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text('النوع: ${_getSystemTypeName(system.type)}'),
                  Text('الحالة: ${_getSystemStatusName(system.status)}'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // أزرار التحكم
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: isIrrigating
                      ? null
                      : () {
                          context.read<IrrigationBloc>().add(
                                IrrigationEventStartIrrigation(
                                  systemId: system.id,
                                  trigger: IrrigationTrigger.manual,
                                ),
                              );
                        },
                  icon: Icon(isIrrigating ? Icons.pause : Icons.play_arrow),
                  label: Text(isIrrigating ? 'جاري الري...' : 'بدء الري'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        isIrrigating ? Colors.orange : Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: isIrrigating
                      ? () {
                          context.read<IrrigationBloc>().add(
                                IrrigationEventStopIrrigation(system.id),
                              );
                        }
                      : null,
                  icon: const Icon(Icons.stop),
                  label: const Text('إيقاف الري'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // قراءات الحساسات
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'قراءات الحساسات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildSensorIndicator(
                        icon: FontAwesomeIcons.droplet,
                        title: 'رطوبة التربة',
                        value: system.sensors.soilMoisture,
                        color: _getMoistureColor(system.sensors.soilMoisture),
                        unit: '%',
                      ),
                      _buildSensorIndicator(
                        icon: FontAwesomeIcons.temperatureHalf,
                        title: 'درجة الحرارة',
                        value: system.sensors.temperature / 50,
                        color: _getTemperatureColor(system.sensors.temperature),
                        unit: '°C',
                        actualValue: system.sensors.temperature,
                      ),
                      _buildSensorIndicator(
                        icon: FontAwesomeIcons.water,
                        title: 'منسوب المياه',
                        value: system.sensors.waterLevel,
                        color: _getWaterLevelColor(system.sensors.waterLevel),
                        unit: '%',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر الحساس
  Widget _buildSensorIndicator({
    required IconData icon,
    required String title,
    required double value,
    required Color color,
    required String unit,
    double? actualValue,
  }) {
    final displayValue = actualValue ?? (value * 100);

    return Column(
      children: [
        CircularPercentIndicator(
          radius: 35.0,
          lineWidth: 6.0,
          percent: value.clamp(0.0, 1.0),
          center: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                displayValue.toStringAsFixed(1),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              Text(
                unit,
                style: const TextStyle(fontSize: 10),
              ),
            ],
          ),
          progressColor: color,
          backgroundColor: Colors.grey.shade200,
          circularStrokeCap: CircularStrokeCap.round,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // ===== الدوال المساعدة =====

  /// الحصول على لون النظام حسب النوع
  Color _getSystemColor(String type) {
    switch (type) {
      case 'drip':
        return Colors.blue;
      case 'sprinkler':
        return Colors.green;
      case 'micro':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة النظام حسب النوع
  IconData _getSystemIcon(String type) {
    switch (type) {
      case 'drip':
        return FontAwesomeIcons.droplet;
      case 'sprinkler':
        return FontAwesomeIcons.shower;
      case 'micro':
        return FontAwesomeIcons.seedling;
      default:
        return FontAwesomeIcons.water;
    }
  }

  /// الحصول على اسم نوع النظام
  String _getSystemTypeName(String type) {
    switch (type) {
      case 'drip':
        return 'ري بالتنقيط';
      case 'sprinkler':
        return 'ري بالرش';
      case 'micro':
        return 'ري دقيق';
      default:
        return 'غير محدد';
    }
  }

  /// الحصول على اسم حالة النظام
  String _getSystemStatusName(String status) {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'inactive':
        return 'غير نشط';
      case 'maintenance':
        return 'صيانة';
      default:
        return 'غير محدد';
    }
  }

  /// الحصول على لون رطوبة التربة
  Color _getMoistureColor(double value) {
    if (value < 0.3) return Colors.red;
    if (value < 0.4) return Colors.orange;
    if (value < 0.6) return Colors.green;
    return Colors.blue;
  }

  /// الحصول على لون درجة الحرارة
  Color _getTemperatureColor(double value) {
    if (value < 15) return Colors.blue;
    if (value < 25) return Colors.green;
    if (value < 35) return Colors.orange;
    return Colors.red;
  }

  /// الحصول على لون منسوب المياه
  Color _getWaterLevelColor(double value) {
    if (value < 0.2) return Colors.red;
    if (value < 0.4) return Colors.orange;
    return Colors.blue;
  }
}
