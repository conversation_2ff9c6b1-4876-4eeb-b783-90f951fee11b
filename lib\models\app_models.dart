import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class User extends Equatable {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String? imageUrl;
  final DateTime createdAt;
  final DateTime lastLogin;

  const User({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.imageUrl,
    required this.createdAt,
    required this.lastLogin,
  });

  factory User.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return User(
      id: doc.id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      phone: data['phone'],
      imageUrl: data['imageUrl'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      lastLogin: (data['lastLogin'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'email': email,
      'phone': phone,
      'imageUrl': imageUrl,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastLogin': Timestamp.fromDate(lastLogin),
    };
  }

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? imageUrl,
    DateTime? createdAt,
    DateTime? lastLogin,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }

  @override
  List<Object?> get props =>
      [id, name, email, phone, imageUrl, createdAt, lastLogin];
}

class Product extends Equatable {
  final String id;
  final String name;
  final String description;
  final double price;
  final String unit;
  final double quantity;
  final String category;
  final List<String> imageUrls;
  final String location;
  final DateTime createdAt;
  final String sellerId;
  final String? sellerName;
  final String? sellerPhone;

  const Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.unit,
    required this.quantity,
    required this.category,
    required this.imageUrls,
    required this.location,
    required this.createdAt,
    required this.sellerId,
    this.sellerName,
    this.sellerPhone,
  });

  factory Product.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Product(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      price: (data['price'] ?? 0).toDouble(),
      unit: data['unit'] ?? 'كجم',
      quantity: (data['quantity'] ?? 0).toDouble(),
      category: data['category'] ?? '',
      imageUrls: List<String>.from(data['imageUrls'] ?? []),
      location: data['location'] ?? '',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      sellerId: data['sellerId'] ?? '',
      sellerName: data['sellerName'],
      sellerPhone: data['sellerPhone'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'price': price,
      'unit': unit,
      'quantity': quantity,
      'category': category,
      'imageUrls': imageUrls,
      'location': location,
      'createdAt': Timestamp.fromDate(createdAt),
      'sellerId': sellerId,
      'sellerName': sellerName,
      'sellerPhone': sellerPhone,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        price,
        unit,
        quantity,
        category,
        imageUrls,
        location,
        createdAt,
        sellerId,
        sellerName,
        sellerPhone
      ];
}

class Chat extends Equatable {
  final String id;
  final List<String> participants;
  final String lastMessage;
  final DateTime lastMessageTime;
  final String lastSenderId;
  final Map<String, dynamic> unreadCount;

  const Chat({
    required this.id,
    required this.participants,
    required this.lastMessage,
    required this.lastMessageTime,
    required this.lastSenderId,
    required this.unreadCount,
  });

  factory Chat.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    Map<String, dynamic> unreadCountMap = {};
    final unreadCountData = data['unreadCount'];

    if (unreadCountData is Map) {
      unreadCountMap = Map<String, dynamic>.from(unreadCountData);
    } else if (unreadCountData != null) {
      // Ignore invalid unreadCount format
    }

    return Chat(
      id: doc.id,
      participants: List<String>.from(data['participants'] ?? []),
      lastMessage: data['lastMessage'] ?? '',
      lastMessageTime: data['lastMessageTime'] != null
          ? (data['lastMessageTime'] as Timestamp).toDate()
          : DateTime.now(),
      lastSenderId: data['lastSenderId'] ?? '',
      unreadCount: unreadCountMap,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'participants': participants,
      'lastMessage': lastMessage,
      'lastMessageTime': Timestamp.fromDate(lastMessageTime),
      'lastSenderId': lastSenderId,
      'unreadCount': unreadCount,
    };
  }

  @override
  List<Object?> get props => [
        id,
        participants,
        lastMessage,
        lastMessageTime,
        lastSenderId,
        unreadCount
      ];
}

class Message extends Equatable {
  final String id;
  final String chatId;
  final String senderId;
  final String content;
  final DateTime timestamp;
  final bool isRead;
  final List<String>? attachmentUrls;
  final String? attachmentType;

  const Message({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.content,
    required this.timestamp,
    required this.isRead,
    this.attachmentUrls,
    this.attachmentType,
  });

  factory Message.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Message(
      id: doc.id,
      chatId: data['chatId'] ?? '',
      senderId: data['senderId'] ?? '',
      content: data['content'] ?? '',
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      isRead: data['isRead'] ?? false,
      attachmentUrls: data['attachmentUrls'] != null
          ? List<String>.from(data['attachmentUrls'])
          : null,
      attachmentType: data['attachmentType'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'chatId': chatId,
      'senderId': senderId,
      'content': content,
      'timestamp': Timestamp.fromDate(timestamp),
      'isRead': isRead,
      'attachmentUrls': attachmentUrls,
      'attachmentType': attachmentType,
    };
  }

  @override
  List<Object?> get props => [
        id,
        chatId,
        senderId,
        content,
        timestamp,
        isRead,
        attachmentUrls,
        attachmentType
      ];
}

class TempFileData {
  final Uint8List bytes; // البيانات الثنائية للملف
  final String fileName; // اسم الملف
  final String mimeType; // نوع الملف (مثل image/jpeg)

  // منشئ الفئة
  TempFileData({
    required this.bytes,
    required this.fileName,
    required this.mimeType,
  });
}

// ===== نماذج بيانات نظام الري الذكي =====

/// نموذج بيانات نظام الري
class IrrigationSystem extends Equatable {
  final String id;
  final String name;
  final String type; // drip, sprinkler, micro
  final String status; // active, inactive, maintenance
  final String serialNumber;
  final DateTime lastUpdate;
  final String? description;
  final String? location;
  final String? cropType;
  final SensorData sensors;

  const IrrigationSystem({
    required this.id,
    required this.name,
    required this.type,
    required this.status,
    required this.serialNumber,
    required this.lastUpdate,
    required this.sensors,
    this.description,
    this.location,
    this.cropType,
  });

  factory IrrigationSystem.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return IrrigationSystem(
      id: doc.id,
      name: data['name'] ?? '',
      type: data['type'] ?? 'drip',
      status: data['status'] ?? 'inactive',
      serialNumber: data['serialNumber'] ?? '',
      lastUpdate: (data['lastUpdate'] as Timestamp).toDate(),
      description: data['description'],
      location: data['location'],
      cropType: data['cropType'],
      sensors: SensorData.fromMap(data['sensors'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'type': type,
      'status': status,
      'serialNumber': serialNumber,
      'lastUpdate': Timestamp.fromDate(lastUpdate),
      'description': description,
      'location': location,
      'cropType': cropType,
      'sensors': sensors.toMap(),
    };
  }

  IrrigationSystem copyWith({
    String? id,
    String? name,
    String? type,
    String? status,
    String? serialNumber,
    DateTime? lastUpdate,
    String? description,
    String? location,
    String? cropType,
    SensorData? sensors,
  }) {
    return IrrigationSystem(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      status: status ?? this.status,
      serialNumber: serialNumber ?? this.serialNumber,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      description: description ?? this.description,
      location: location ?? this.location,
      cropType: cropType ?? this.cropType,
      sensors: sensors ?? this.sensors,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        type,
        status,
        serialNumber,
        lastUpdate,
        description,
        location,
        cropType,
        sensors
      ];
}

/// نموذج بيانات الحساسات
class SensorData extends Equatable {
  final double soilMoisture; // رطوبة التربة (0.0 - 1.0)
  final double temperature; // درجة الحرارة (مئوية)
  final double humidity; // رطوبة الهواء (0.0 - 1.0)
  final double waterLevel; // منسوب المياه (0.0 - 1.0)
  final double batteryLevel; // مستوى البطارية (0.0 - 1.0)
  final bool rainStatus; // حالة المطر
  final double? ph; // درجة الحموضة
  final double? lightIntensity; // شدة الإضاءة

  const SensorData({
    required this.soilMoisture,
    required this.temperature,
    required this.humidity,
    required this.waterLevel,
    required this.batteryLevel,
    required this.rainStatus,
    this.ph,
    this.lightIntensity,
  });

  factory SensorData.fromMap(Map<String, dynamic> map) {
    return SensorData(
      soilMoisture: (map['soil_moisture'] ?? 0.0).toDouble(),
      temperature: (map['temperature'] ?? 0.0).toDouble(),
      humidity: (map['humidity'] ?? 0.0).toDouble(),
      waterLevel: (map['water_level'] ?? 0.0).toDouble(),
      batteryLevel: (map['battery_level'] ?? 0.0).toDouble(),
      rainStatus: map['rain_status'] ?? false,
      ph: map['ph']?.toDouble(),
      lightIntensity: map['light_intensity']?.toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'soil_moisture': soilMoisture,
      'temperature': temperature,
      'humidity': humidity,
      'water_level': waterLevel,
      'battery_level': batteryLevel,
      'rain_status': rainStatus,
      'ph': ph,
      'light_intensity': lightIntensity,
    };
  }

  SensorData copyWith({
    double? soilMoisture,
    double? temperature,
    double? humidity,
    double? waterLevel,
    double? batteryLevel,
    bool? rainStatus,
    double? ph,
    double? lightIntensity,
  }) {
    return SensorData(
      soilMoisture: soilMoisture ?? this.soilMoisture,
      temperature: temperature ?? this.temperature,
      humidity: humidity ?? this.humidity,
      waterLevel: waterLevel ?? this.waterLevel,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      rainStatus: rainStatus ?? this.rainStatus,
      ph: ph ?? this.ph,
      lightIntensity: lightIntensity ?? this.lightIntensity,
    );
  }

  @override
  List<Object?> get props => [
        soilMoisture,
        temperature,
        humidity,
        waterLevel,
        batteryLevel,
        rainStatus,
        ph,
        lightIntensity
      ];
}

/// نموذج بيانات سجل الري
class IrrigationRecord extends Equatable {
  final String id;
  final String systemId;
  final DateTime startTime;
  final DateTime? endTime;
  final int duration; // بالدقائق
  final double waterUsed; // باللتر
  final IrrigationTrigger trigger;
  final String? notes;
  final Map<String, dynamic>? sensorDataAtStart;

  const IrrigationRecord({
    required this.id,
    required this.systemId,
    required this.startTime,
    this.endTime,
    required this.duration,
    required this.waterUsed,
    required this.trigger,
    this.notes,
    this.sensorDataAtStart,
  });

  factory IrrigationRecord.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return IrrigationRecord(
      id: doc.id,
      systemId: data['systemId'] ?? '',
      startTime: (data['startTime'] as Timestamp).toDate(),
      endTime: data['endTime'] != null
          ? (data['endTime'] as Timestamp).toDate()
          : null,
      duration: data['duration'] ?? 0,
      waterUsed: (data['waterUsed'] ?? 0.0).toDouble(),
      trigger: IrrigationTrigger.values.firstWhere(
        (e) => e.toString().split('.').last == data['trigger'],
        orElse: () => IrrigationTrigger.manual,
      ),
      notes: data['notes'],
      sensorDataAtStart: data['sensorDataAtStart'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'systemId': systemId,
      'startTime': Timestamp.fromDate(startTime),
      'endTime': endTime != null ? Timestamp.fromDate(endTime!) : null,
      'duration': duration,
      'waterUsed': waterUsed,
      'trigger': trigger.toString().split('.').last,
      'notes': notes,
      'sensorDataAtStart': sensorDataAtStart,
    };
  }

  @override
  List<Object?> get props => [
        id,
        systemId,
        startTime,
        endTime,
        duration,
        waterUsed,
        trigger,
        notes,
        sensorDataAtStart
      ];
}

/// أنواع مشغلات الري
enum IrrigationTrigger {
  manual, // يدوي
  auto, // تلقائي
  smart, // ذكي
  scheduled, // مجدول
}

/// نموذج بيانات أنواع النباتات
class PlantType extends Equatable {
  final String id;
  final String name;
  final String arabicName;
  final String description;
  final String category; // vegetables, fruits, grains, etc.
  final double optimalMoisture; // الرطوبة المثلى (0.0 - 1.0)
  final double minTemperature; // أدنى درجة حرارة
  final double maxTemperature; // أعلى درجة حرارة
  final double optimalPh; // درجة الحموضة المثلى
  final int growthDurationDays; // مدة النمو بالأيام
  final double waterRequirementPerDay; // احتياج المياه اليومي (لتر/متر مربع)
  final List<String> commonDiseases; // الأمراض الشائعة
  final List<String> irrigationTips; // نصائح الري
  final String? imageUrl;
  final DateTime createdAt;

  const PlantType({
    required this.id,
    required this.name,
    required this.arabicName,
    required this.description,
    required this.category,
    required this.optimalMoisture,
    required this.minTemperature,
    required this.maxTemperature,
    required this.optimalPh,
    required this.growthDurationDays,
    required this.waterRequirementPerDay,
    required this.commonDiseases,
    required this.irrigationTips,
    required this.createdAt,
    this.imageUrl,
  });

  factory PlantType.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return PlantType(
      id: doc.id,
      name: data['name'] ?? '',
      arabicName: data['arabicName'] ?? '',
      description: data['description'] ?? '',
      category: data['category'] ?? '',
      optimalMoisture: (data['optimalMoisture'] ?? 0.5).toDouble(),
      minTemperature: (data['minTemperature'] ?? 15.0).toDouble(),
      maxTemperature: (data['maxTemperature'] ?? 35.0).toDouble(),
      optimalPh: (data['optimalPh'] ?? 7.0).toDouble(),
      growthDurationDays: data['growthDurationDays'] ?? 90,
      waterRequirementPerDay: (data['waterRequirementPerDay'] ?? 5.0).toDouble(),
      commonDiseases: List<String>.from(data['commonDiseases'] ?? []),
      irrigationTips: List<String>.from(data['irrigationTips'] ?? []),
      imageUrl: data['imageUrl'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'arabicName': arabicName,
      'description': description,
      'category': category,
      'optimalMoisture': optimalMoisture,
      'minTemperature': minTemperature,
      'maxTemperature': maxTemperature,
      'optimalPh': optimalPh,
      'growthDurationDays': growthDurationDays,
      'waterRequirementPerDay': waterRequirementPerDay,
      'commonDiseases': commonDiseases,
      'irrigationTips': irrigationTips,
      'imageUrl': imageUrl,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        arabicName,
        description,
        category,
        optimalMoisture,
        minTemperature,
        maxTemperature,
        optimalPh,
        growthDurationDays,
        waterRequirementPerDay,
        commonDiseases,
        irrigationTips,
        imageUrl,
        createdAt,
      ];
}

/// نموذج بيانات إحصائيات استخدام المياه
class WaterUsageStats extends Equatable {
  final String id;
  final String systemId;
  final DateTime date;
  final double totalWaterUsed; // إجمالي المياه المستخدمة (لتر)
  final int totalIrrigationSessions; // عدد جلسات الري
  final double averageSessionDuration; // متوسط مدة الجلسة (دقيقة)
  final double efficiency; // كفاءة الري (0.0 - 1.0)
  final Map<String, double> hourlyUsage; // الاستخدام بالساعة
  final double costEstimate; // تقدير التكلفة
  final String notes;

  const WaterUsageStats({
    required this.id,
    required this.systemId,
    required this.date,
    required this.totalWaterUsed,
    required this.totalIrrigationSessions,
    required this.averageSessionDuration,
    required this.efficiency,
    required this.hourlyUsage,
    required this.costEstimate,
    required this.notes,
  });

  factory WaterUsageStats.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return WaterUsageStats(
      id: doc.id,
      systemId: data['systemId'] ?? '',
      date: (data['date'] as Timestamp).toDate(),
      totalWaterUsed: (data['totalWaterUsed'] ?? 0.0).toDouble(),
      totalIrrigationSessions: data['totalIrrigationSessions'] ?? 0,
      averageSessionDuration: (data['averageSessionDuration'] ?? 0.0).toDouble(),
      efficiency: (data['efficiency'] ?? 0.0).toDouble(),
      hourlyUsage: Map<String, double>.from(data['hourlyUsage'] ?? {}),
      costEstimate: (data['costEstimate'] ?? 0.0).toDouble(),
      notes: data['notes'] ?? '',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'systemId': systemId,
      'date': Timestamp.fromDate(date),
      'totalWaterUsed': totalWaterUsed,
      'totalIrrigationSessions': totalIrrigationSessions,
      'averageSessionDuration': averageSessionDuration,
      'efficiency': efficiency,
      'hourlyUsage': hourlyUsage,
      'costEstimate': costEstimate,
      'notes': notes,
    };
  }

  @override
  List<Object?> get props => [
        id,
        systemId,
        date,
        totalWaterUsed,
        totalIrrigationSessions,
        averageSessionDuration,
        efficiency,
        hourlyUsage,
        costEstimate,
        notes,
      ];
}

/// نموذج بيانات قراءات الحساسات التاريخية
class SensorReading extends Equatable {
  final String id;
  final String systemId;
  final DateTime timestamp;
  final SensorData sensorData;
  final String? notes;
  final bool isAlert; // تنبيه في حالة وجود قراءات غير طبيعية

  const SensorReading({
    required this.id,
    required this.systemId,
    required this.timestamp,
    required this.sensorData,
    required this.isAlert,
    this.notes,
  });

  factory SensorReading.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return SensorReading(
      id: doc.id,
      systemId: data['systemId'] ?? '',
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      sensorData: SensorData.fromMap(data['sensorData'] ?? {}),
      notes: data['notes'],
      isAlert: data['isAlert'] ?? false,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'systemId': systemId,
      'timestamp': Timestamp.fromDate(timestamp),
      'sensorData': sensorData.toMap(),
      'notes': notes,
      'isAlert': isAlert,
    };
  }

  @override
  List<Object?> get props => [
        id,
        systemId,
        timestamp,
        sensorData,
        notes,
        isAlert,
      ];
}

/// نموذج بيانات إعدادات الري
class IrrigationSettings extends Equatable {
  final String systemId;
  final bool autoMode;
  final bool manualMode;
  final double moistureThreshold; // عتبة الرطوبة للري التلقائي
  final int defaultDuration; // مدة الري الافتراضية بالدقائق
  final List<ScheduledIrrigation> schedule;
  final bool rainSensorEnabled;
  final double maxWaterPerDay; // الحد الأقصى للمياه يومياً

  const IrrigationSettings({
    required this.systemId,
    required this.autoMode,
    required this.manualMode,
    required this.moistureThreshold,
    required this.defaultDuration,
    required this.schedule,
    required this.rainSensorEnabled,
    required this.maxWaterPerDay,
  });

  factory IrrigationSettings.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return IrrigationSettings(
      systemId: data['systemId'] ?? '',
      autoMode: data['autoMode'] ?? false,
      manualMode: data['manualMode'] ?? false,
      moistureThreshold: (data['moistureThreshold'] ?? 0.4).toDouble(),
      defaultDuration: data['defaultDuration'] ?? 30,
      schedule: (data['schedule'] as List<dynamic>? ?? [])
          .map((item) => ScheduledIrrigation.fromMap(item))
          .toList(),
      rainSensorEnabled: data['rainSensorEnabled'] ?? true,
      maxWaterPerDay: (data['maxWaterPerDay'] ?? 500.0).toDouble(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'systemId': systemId,
      'autoMode': autoMode,
      'manualMode': manualMode,
      'moistureThreshold': moistureThreshold,
      'defaultDuration': defaultDuration,
      'schedule': schedule.map((item) => item.toMap()).toList(),
      'rainSensorEnabled': rainSensorEnabled,
      'maxWaterPerDay': maxWaterPerDay,
    };
  }

  @override
  List<Object?> get props => [
        systemId,
        autoMode,
        manualMode,
        moistureThreshold,
        defaultDuration,
        schedule,
        rainSensorEnabled,
        maxWaterPerDay
      ];
}

/// نموذج بيانات الري المجدول
class ScheduledIrrigation extends Equatable {
  final String id;
  final TimeOfDay time;
  final int duration; // بالدقائق
  final List<int> daysOfWeek; // 1-7 (الاثنين-الأحد)
  final bool isActive;

  const ScheduledIrrigation({
    required this.id,
    required this.time,
    required this.duration,
    required this.daysOfWeek,
    required this.isActive,
  });

  factory ScheduledIrrigation.fromMap(Map<String, dynamic> map) {
    return ScheduledIrrigation(
      id: map['id'] ?? '',
      time: TimeOfDay(
        hour: map['hour'] ?? 0,
        minute: map['minute'] ?? 0,
      ),
      duration: map['duration'] ?? 30,
      daysOfWeek: List<int>.from(map['daysOfWeek'] ?? []),
      isActive: map['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'hour': time.hour,
      'minute': time.minute,
      'duration': duration,
      'daysOfWeek': daysOfWeek,
      'isActive': isActive,
    };
  }

  @override
  List<Object?> get props => [id, time, duration, daysOfWeek, isActive];
}

// ===== نماذج بيانات تشخيص أمراض النباتات =====

/// نموذج بيانات معلومات المرض
class DiseaseInfo extends Equatable {
  final String id;
  final String name;
  final String arabicName;
  final String description;
  final List<String> symptoms;
  final List<String> treatment;
  final String? imageUrl;
  final DateTime createdAt;

  const DiseaseInfo({
    required this.id,
    required this.name,
    required this.arabicName,
    required this.description,
    required this.symptoms,
    required this.treatment,
    this.imageUrl,
    required this.createdAt,
  });

  factory DiseaseInfo.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DiseaseInfo(
      id: doc.id,
      name: data['name'] ?? '',
      arabicName: data['arabicName'] ?? '',
      description: data['description'] ?? '',
      symptoms: List<String>.from(data['symptoms'] ?? []),
      treatment: List<String>.from(data['treatment'] ?? []),
      imageUrl: data['imageUrl'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'arabicName': arabicName,
      'description': description,
      'symptoms': symptoms,
      'treatment': treatment,
      'imageUrl': imageUrl,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        arabicName,
        description,
        symptoms,
        treatment,
        imageUrl,
        createdAt,
      ];
}

/// نموذج بيانات نتيجة التشخيص
class DiagnosisResult extends Equatable {
  final String id;
  final String userId;
  final String requestImageUrl;
  final DateTime createdAt;
  final String status; // 'processing', 'completed', 'failed'
  final String? detectedDiseaseId;
  final double? confidence;
  final String? plantPart; // e.g., 'leaf', 'stem', 'fruit'

  const DiagnosisResult({
    required this.id,
    required this.userId,
    required this.requestImageUrl,
    required this.createdAt,
    required this.status,
    this.detectedDiseaseId,
    this.confidence,
    this.plantPart,
  });

  factory DiagnosisResult.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DiagnosisResult(
      id: doc.id,
      userId: data['userId'] ?? '',
      requestImageUrl: data['requestImageUrl'] ?? '',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      status: data['status'] ?? 'failed',
      detectedDiseaseId: data['detectedDiseaseId'],
      confidence: (data['confidence'] ?? 0.0).toDouble(),
      plantPart: data['plantPart'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'requestImageUrl': requestImageUrl,
      'createdAt': Timestamp.fromDate(createdAt),
      'status': status,
      'detectedDiseaseId': detectedDiseaseId,
      'confidence': confidence,
      'plantPart': plantPart,
    };
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        requestImageUrl,
        createdAt,
        status,
        detectedDiseaseId,
        confidence,
        plantPart,
      ];
}
