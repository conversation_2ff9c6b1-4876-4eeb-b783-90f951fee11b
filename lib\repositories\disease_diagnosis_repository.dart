import 'dart:typed_data';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:sam03/models/app_models.dart';
import 'package:sam03/core/constants/app_constants.dart';
import 'package:uuid/uuid.dart';

/// مستودع بيانات تشخيص أمراض النباتات
/// يتولى جميع عمليات الوصول للبيانات المتعلقة بتشخيص الأمراض
class DiseaseDiagnosisRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final Uuid _uuid = const Uuid();

  /// Uploads a diagnosis image to Firebase Storage and returns the download URL.
  Future<String> uploadDiagnosisImage(
      Uint8List imageBytes, String userId) async {
    final fileName = '${_uuid.v4()}.jpg';
    final ref = _storage.ref().child('diagnosis_images/$userId/$fileName');

    final metadata = SettableMetadata(contentType: 'image/jpeg');

    final uploadTask = ref.putData(imageBytes, metadata);
    final snapshot = await uploadTask.whenComplete(() => null);
    final downloadUrl = await snapshot.ref.getDownloadURL();
    return downloadUrl;
  }

  /// إنشاء طلب تشخيص جديد في Firestore
  Future<DiagnosisResult> createDiagnosisRequest({
    required String userId,
    required String imageUrl,
    String? plantPart,
  }) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.collectionDiagnosisResults)
          .add({
        'userId': userId,
        'requestImageUrl': imageUrl,
        'createdAt': FieldValue.serverTimestamp(),
        'status': 'processing',
        'detectedDiseaseId': null,
        'confidence': null,
        'plantPart': plantPart,
      });

      final doc = await docRef.get();
      return DiagnosisResult.fromFirestore(doc);
    } catch (e) {
      throw Exception('فشل في إنشاء طلب التشخيص: $e');
    }
  }

  /// جلب تاريخ التشخيص لمستخدم محدد
  Future<List<DiagnosisResult>> getDiagnosisHistory(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collectionDiagnosisResults)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => DiagnosisResult.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب تاريخ التشخيص: $e');
    }
  }

  /// جلب معلومات مفصلة عن مرض محدد
  Future<DiseaseInfo?> getDiseaseInfo(String diseaseId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.collectionDiseaseInfo)
          .doc(diseaseId)
          .get();

      if (doc.exists) {
        return DiseaseInfo.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب معلومات المرض: $e');
    }
  }

  /// تحديث نتيجة التشخيص بعد المعالجة
  Future<void> updateDiagnosisResult({
    required String resultId,
    required String status,
    String? detectedDiseaseId,
    double? confidence,
  }) async {
    try {
      await _firestore
          .collection(AppConstants.collectionDiagnosisResults)
          .doc(resultId)
          .update({
        'status': status,
        'detectedDiseaseId': detectedDiseaseId,
        'confidence': confidence,
      });
    } catch (e) {
      throw Exception('فشل في تحديث نتيجة التشخيص: $e');
    }
  }

  // ===== دوال إضافية =====

  /// جلب جميع معلومات الأمراض
  Future<List<DiseaseInfo>> getAllDiseases() async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collectionDiseaseInfo)
          .orderBy('arabicName')
          .get();

      return snapshot.docs
          .map((doc) => DiseaseInfo.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب قائمة الأمراض: $e');
    }
  }

  /// البحث في الأمراض
  Future<List<DiseaseInfo>> searchDiseases(String query) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collectionDiseaseInfo)
          .where('arabicName', isGreaterThanOrEqualTo: query)
          .where('arabicName', isLessThan: query + '\uf8ff')
          .get();

      return snapshot.docs
          .map((doc) => DiseaseInfo.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('فشل في البحث عن الأمراض: $e');
    }
  }

  /// جلب نتيجة تشخيص محددة
  Future<DiagnosisResult?> getDiagnosisResult(String resultId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.collectionDiagnosisResults)
          .doc(resultId)
          .get();

      if (doc.exists) {
        return DiagnosisResult.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب نتيجة التشخيص: $e');
    }
  }

  /// حذف نتيجة تشخيص
  Future<void> deleteDiagnosisResult(String resultId) async {
    try {
      await _firestore
          .collection(AppConstants.collectionDiagnosisResults)
          .doc(resultId)
          .delete();
    } catch (e) {
      throw Exception('فشل في حذف نتيجة التشخيص: $e');
    }
  }

  /// إضافة معلومات مرض جديد
  Future<DiseaseInfo> addDiseaseInfo(DiseaseInfo diseaseInfo) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.collectionDiseaseInfo)
          .add(diseaseInfo.toFirestore());

      final doc = await docRef.get();
      return DiseaseInfo.fromFirestore(doc);
    } catch (e) {
      throw Exception('فشل في إضافة معلومات المرض: $e');
    }
  }

  /// تحديث معلومات مرض
  Future<void> updateDiseaseInfo(DiseaseInfo diseaseInfo) async {
    try {
      await _firestore
          .collection(AppConstants.collectionDiseaseInfo)
          .doc(diseaseInfo.id)
          .update(diseaseInfo.toFirestore());
    } catch (e) {
      throw Exception('فشل في تحديث معلومات المرض: $e');
    }
  }

  /// جلب إحصائيات التشخيص للمستخدم
  Future<Map<String, dynamic>> getDiagnosisStats(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collectionDiagnosisResults)
          .where('userId', isEqualTo: userId)
          .get();

      final results = snapshot.docs
          .map((doc) => DiagnosisResult.fromFirestore(doc))
          .toList();

      int totalDiagnoses = results.length;
      int completedDiagnoses =
          results.where((r) => r.status == 'completed').length;
      int processingDiagnoses =
          results.where((r) => r.status == 'processing').length;
      int failedDiagnoses = results.where((r) => r.status == 'failed').length;

      // حساب متوسط الثقة
      final completedResults = results.where((r) => r.confidence != null);
      double averageConfidence = 0.0;
      if (completedResults.isNotEmpty) {
        averageConfidence =
            completedResults.map((r) => r.confidence!).reduce((a, b) => a + b) /
                completedResults.length;
      }

      return {
        'totalDiagnoses': totalDiagnoses,
        'completedDiagnoses': completedDiagnoses,
        'processingDiagnoses': processingDiagnoses,
        'failedDiagnoses': failedDiagnoses,
        'averageConfidence': averageConfidence,
        'successRate':
            totalDiagnoses > 0 ? completedDiagnoses / totalDiagnoses : 0.0,
      };
    } catch (e) {
      throw Exception('فشل في جلب إحصائيات التشخيص: $e');
    }
  }
}
