import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:sam03/core/constants/app_constants.dart';
import 'package:sam03/models/app_models.dart';

/// أدوات مساعدة لنظام الري الذكي
class IrrigationUtils {
  
  // ===== دوال الألوان =====

  /// الحصول على لون نوع النظام
  static Color getSystemTypeColor(String type) {
    final colorValue = AppConstants.systemColors[type];
    return colorValue != null ? Color(colorValue) : Colors.grey;
  }

  /// الحصول على لون حالة النظام
  static Color getSystemStatusColor(String status) {
    switch (status) {
      case AppConstants.irrigationStatusActive:
        return Colors.green;
      case AppConstants.irrigationStatusInactive:
        return Colors.grey;
      case AppConstants.irrigationStatusMaintenance:
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على لون رطوبة التربة
  static Color getSoilMoistureColor(double value) {
    if (value < AppConstants.moistureThresholdLow) {
      return Colors.red;
    } else if (value < AppConstants.moistureThresholdMedium) {
      return Colors.orange;
    } else if (value < AppConstants.moistureThresholdHigh) {
      return Colors.green;
    } else {
      return Colors.blue;
    }
  }

  /// الحصول على لون درجة الحرارة
  static Color getTemperatureColor(double value) {
    if (value < AppConstants.temperatureThresholdCold) {
      return Colors.blue;
    } else if (value < AppConstants.temperatureThresholdNormal) {
      return Colors.green;
    } else if (value < AppConstants.temperatureThresholdWarm) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  /// الحصول على لون منسوب المياه
  static Color getWaterLevelColor(double value) {
    if (value < AppConstants.waterLevelThresholdLow) {
      return Colors.red;
    } else if (value < AppConstants.waterLevelThresholdMedium) {
      return Colors.orange;
    } else {
      return Colors.blue;
    }
  }

  /// الحصول على لون البطارية
  static Color getBatteryColor(double value) {
    if (value < AppConstants.batteryThresholdLow) {
      return Colors.red;
    } else if (value < AppConstants.batteryThresholdMedium) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  /// الحصول على لون نوع الري
  static Color getIrrigationTriggerColor(IrrigationTrigger trigger) {
    switch (trigger) {
      case IrrigationTrigger.manual:
        return Colors.blue;
      case IrrigationTrigger.auto:
        return Colors.green;
      case IrrigationTrigger.smart:
        return Colors.purple;
      case IrrigationTrigger.scheduled:
        return Colors.orange;
    }
  }

  // ===== دوال الأيقونات =====

  /// الحصول على أيقونة نوع النظام
  static IconData getSystemTypeIcon(String type) {
    switch (type) {
      case AppConstants.irrigationTypeDrip:
        return FontAwesomeIcons.droplet;
      case AppConstants.irrigationTypeSprinkler:
        return FontAwesomeIcons.shower;
      case AppConstants.irrigationTypeMicro:
        return FontAwesomeIcons.seedling;
      default:
        return FontAwesomeIcons.water;
    }
  }

  /// الحصول على أيقونة حالة النظام
  static IconData getSystemStatusIcon(String status) {
    switch (status) {
      case AppConstants.irrigationStatusActive:
        return Icons.check_circle;
      case AppConstants.irrigationStatusInactive:
        return Icons.circle_outlined;
      case AppConstants.irrigationStatusMaintenance:
        return Icons.build_circle;
      default:
        return Icons.help_outline;
    }
  }

  /// الحصول على أيقونة البطارية
  static IconData getBatteryIcon(double value) {
    if (value < 0.2) {
      return Icons.battery_0_bar;
    } else if (value < 0.4) {
      return Icons.battery_2_bar;
    } else if (value < 0.6) {
      return Icons.battery_4_bar;
    } else if (value < 0.8) {
      return Icons.battery_5_bar;
    } else {
      return Icons.battery_full;
    }
  }

  /// الحصول على أيقونة نوع الري
  static IconData getIrrigationTriggerIcon(IrrigationTrigger trigger) {
    switch (trigger) {
      case IrrigationTrigger.manual:
        return Icons.touch_app;
      case IrrigationTrigger.auto:
        return Icons.auto_mode;
      case IrrigationTrigger.smart:
        return FontAwesomeIcons.wandMagicSparkles;
      case IrrigationTrigger.scheduled:
        return Icons.schedule;
    }
  }

  /// الحصول على أيقونة حالة الطقس
  static IconData getWeatherIcon(bool isRaining) {
    return isRaining ? FontAwesomeIcons.cloudRain : FontAwesomeIcons.sun;
  }

  // ===== دوال النصوص =====

  /// الحصول على اسم نوع النظام
  static String getSystemTypeName(String type) {
    return AppConstants.irrigationTypeNames[type] ?? 'غير محدد';
  }

  /// الحصول على اسم حالة النظام
  static String getSystemStatusName(String status) {
    return AppConstants.irrigationStatusNames[status] ?? 'غير محدد';
  }

  /// الحصول على اسم نوع الري
  static String getIrrigationTriggerName(IrrigationTrigger trigger) {
    return AppConstants.irrigationTriggerNames[trigger.toString().split('.').last] ?? 'غير محدد';
  }

  /// الحصول على اسم يوم الأسبوع
  static String getWeekDayName(int day) {
    return AppConstants.weekDayNames[day] ?? 'غير محدد';
  }

  /// تنسيق النسبة المئوية
  static String formatPercentage(double value) {
    return '${(value * 100).toInt()}${AppConstants.unitPercentage}';
  }

  /// تنسيق درجة الحرارة
  static String formatTemperature(double value) {
    return '${value.toStringAsFixed(1)}${AppConstants.unitCelsius}';
  }

  /// تنسيق كمية المياه
  static String formatWaterAmount(double value) {
    return '${value.toStringAsFixed(1)} ${AppConstants.unitLiters}';
  }

  /// تنسيق المدة بالدقائق
  static String formatDuration(int minutes) {
    if (minutes < 60) {
      return '$minutes ${AppConstants.unitMinutes}';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '$hours ${AppConstants.unitHours}';
      } else {
        return '$hours ${AppConstants.unitHours} $remainingMinutes ${AppConstants.unitMinutes}';
      }
    }
  }

  /// تنسيق التاريخ والوقت
  static String formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// تنسيق التاريخ فقط
  static String formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  /// تنسيق الوقت فقط
  static String formatTime(DateTime dateTime) {
    return '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// تنسيق الوقت النسبي (منذ كم من الوقت)
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return formatDate(dateTime);
    }
  }

  // ===== دوال التحقق =====

  /// التحقق من صحة اسم النظام
  static String? validateSystemName(String? value) {
    if (value == null || value.isEmpty) {
      return ValidationConstants.errorSystemNameRequired;
    }
    if (value.length < ValidationConstants.minSystemNameLength) {
      return ValidationConstants.errorSystemNameTooShort;
    }
    if (value.length > ValidationConstants.maxSystemNameLength) {
      return ValidationConstants.errorSystemNameTooLong;
    }
    return null;
  }

  /// التحقق من صحة الرقم التسلسلي
  static String? validateSerialNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'الرقم التسلسلي مطلوب';
    }
    final regex = RegExp(ValidationConstants.serialNumberPattern);
    if (!regex.hasMatch(value)) {
      return ValidationConstants.errorInvalidSerialNumber;
    }
    return null;
  }

  /// التحقق من صحة عتبة الرطوبة
  static String? validateMoistureThreshold(double? value) {
    if (value == null) {
      return 'عتبة الرطوبة مطلوبة';
    }
    if (value < ValidationConstants.minMoistureThreshold || 
        value > ValidationConstants.maxMoistureThreshold) {
      return ValidationConstants.errorInvalidMoistureThreshold;
    }
    return null;
  }

  /// التحقق من صحة مدة الري
  static String? validateIrrigationDuration(int? value) {
    if (value == null) {
      return 'مدة الري مطلوبة';
    }
    if (value < ValidationConstants.minIrrigationDuration || 
        value > ValidationConstants.maxIrrigationDuration) {
      return ValidationConstants.errorInvalidDuration;
    }
    return null;
  }

  // ===== دوال الحسابات =====

  /// حساب كمية المياه المستهلكة
  static double calculateWaterUsage(int durationMinutes, double flowRate) {
    return durationMinutes * flowRate;
  }

  /// حساب متوسط الاستهلاك اليومي
  static double calculateDailyAverage(List<IrrigationRecord> records, int days) {
    if (records.isEmpty || days <= 0) return 0.0;
    
    final totalWater = records.fold<double>(0.0, (sum, record) => sum + record.waterUsed);
    return totalWater / days;
  }

  /// حساب كفاءة الري
  static double calculateIrrigationEfficiency(double waterUsed, double targetMoisture, double actualMoisture) {
    if (waterUsed <= 0 || targetMoisture <= 0) return 0.0;
    
    final moistureIncrease = actualMoisture - targetMoisture;
    if (moistureIncrease <= 0) return 0.0;
    
    return (moistureIncrease / waterUsed) * 100;
  }

  /// تقدير الوقت المتبقي للري
  static int estimateRemainingTime(double currentMoisture, double targetMoisture, double flowRate) {
    if (currentMoisture >= targetMoisture) return 0;
    
    final moistureDeficit = targetMoisture - currentMoisture;
    // تقدير بسيط: كل لتر يزيد الرطوبة بـ 1%
    final waterNeeded = moistureDeficit * 100;
    return (waterNeeded / flowRate).ceil();
  }
}
